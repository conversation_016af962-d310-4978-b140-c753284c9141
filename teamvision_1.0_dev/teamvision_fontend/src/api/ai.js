import axios from "axios"

/**
 * AI测试用例生成相关API
 */

// AI生成会话管理
export const createAISessionApi = (data) => {
  return axios.post('/api/ai/sessions/', data)
}

export const getAISessionListApi = (params = {}) => {
  return axios.get('/api/ai/sessions/', { params })
}

export const getAISessionDetailApi = (sessionId) => {
  return axios.get(`/api/ai/sessions/${sessionId}/`)
}

export const updateAISessionApi = (sessionId, data) => {
  return axios.put(`/api/ai/sessions/${sessionId}/`, data)
}

export const deleteAISessionApi = (sessionId) => {
  return axios.delete(`/api/ai/sessions/${sessionId}/`)
}

// AI生成测试用例管理
export const getAIGeneratedCasesApi = (sessionId, params = {}) => {
  return axios.get(`/api/ai/sessions/${sessionId}/cases/`, { params })
}

export const getAIGeneratedCaseDetailApi = (caseId) => {
  return axios.get(`/api/ai/cases/${caseId}/`)
}

export const updateAIGeneratedCaseApi = (caseId, data) => {
  return axios.put(`/api/ai/cases/${caseId}/`, data)
}

export const deleteAIGeneratedCaseApi = (caseId) => {
  return axios.delete(`/api/ai/cases/${caseId}/`)
}

// 核心AI功能
export const generateTestCasesApi = (data) => {
  return axios.post('/api/ai/generate/', data)
}

export const acceptTestCasesApi = (data) => {
  return axios.post('/api/ai/accept/', data)
}

export const optimizeTestCaseApi = (data) => {
  return axios.post('/api/ai/optimize/', data)
}

export const analyzeCoverageApi = (data) => {
  return axios.post('/api/ai/analyze-coverage/', data)
}

// AI对话功能
export const chatWithAIApi = (data) => {
  return axios.post('/api/ai/chat/', data)
}

export const getChatHistoryApi = (sessionId) => {
  return axios.get(`/api/ai/sessions/${sessionId}/history/`)
}

// AI使用统计
export const getAIUsageStatisticsApi = (params = {}) => {
  return axios.get('/api/ai/statistics/', { params })
}

/**
 * AI助手快捷操作API
 */

// 快速生成测试用例
export const quickGenerateTestCasesApi = (session_id, projectId, options = {}) => {
  const defaultOptions = {
    generation_type: 'functional',
  }

  const data = {
    session_id: session_id,
    project: projectId,
    ...defaultOptions,
    ...options
  }

  return generateTestCasesApi(data)
}

// 批量采纳测试用例
export const batchAcceptTestCasesApi = (aiCaseIds, parentId = 0, moduleId = 0) => {
  return acceptTestCasesApi({
    ai_case_ids: aiCaseIds,
    parent_id: parentId,
    module_id: moduleId
  })
}

// 智能优化测试用例
export const smartOptimizeTestCaseApi = (aiCaseId, optimizationType = 'quality') => {
  return optimizeTestCaseApi({
    ai_case_id: aiCaseId,
    optimization_type: optimizationType
  })
}

// 分析项目测试覆盖度
export const analyzeProjectCoverageApi = (projectId, requirementText) => {
  return analyzeCoverageApi({
    project_id: projectId,
    requirement_text: requirementText
  })
}

/**
 * AI助手对话相关API
 */

// 发送消息给AI助手
export const sendMessageToAIApi = (sessionId, message) => {
  return chatWithAIApi({
    session_id: sessionId,
    message: message
  })
}

// 获取AI助手建议
export const getAISuggestionsApi = (projectId, context) => {
  return axios.post('/api/ai/suggestions/', {
    project_id: projectId,
    context: context
  })
}

/**
 * AI配置相关API
 */

// 获取AI配置
export const getAIConfigApi = (configType, projectId = null) => {
  const params = { config_type: configType }
  if (projectId) {
    params.project_id = projectId
  }
  return axios.get('/api/ai/config/', { params })
}

// 更新AI配置
export const updateAIConfigApi = (configId, data) => {
  return axios.put(`/api/ai/config/${configId}/`, data)
}

/**
 * AI助手状态管理
 */

// 检查AI服务状态
export const checkAIServiceStatusApi = () => {
  return axios.get('/api/ai/status/')
}

// 获取AI模型信息
export const getAIModelInfoApi = () => {
  return axios.get('/api/ai/model-info/')
}

/**
 * 错误处理和重试机制
 */

// 带重试的API调用
export const callAIApiWithRetry = async (apiFunction, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await apiFunction()
      return response
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
    }
  }
}

// 生成测试用例（带重试）
export const generateTestCasesWithRetryApi = (data) => {
  return callAIApiWithRetry(() => generateTestCasesApi(data))
}

// AI对话（带重试）
export const chatWithAIWithRetryApi = (data) => {
  return callAIApiWithRetry(() => chatWithAIApi(data))
}

/**
 * 批量操作API
 */

// 批量更新AI生成的测试用例状态
export const batchUpdateAICasesStatusApi = (caseIds, status) => {
  return axios.post('/api/ai/cases/batch-update-status/', {
    case_ids: caseIds,
    status: status
  })
}

// 批量删除AI生成的测试用例
export const batchDeleteAICasesApi = (caseIds) => {
  return axios.post('/api/ai/cases/batch-delete/', {
    case_ids: caseIds
  })
}

// 批量评分AI生成的测试用例
export const batchRateAICasesApi = (ratings) => {
  return axios.post('/api/ai/cases/batch-rate/', {
    ratings: ratings  // [{ case_id: 1, rating: 5, feedback: '很好' }]
  })
}

/**
 * 导出功能API
 */

// 导出AI生成的测试用例
export const exportAIGeneratedCasesApi = (sessionId, format = 'excel') => {
  return axios.get(`/api/ai/sessions/${sessionId}/export/`, {
    params: { format },
    responseType: 'blob'
  })
}

// 导出AI对话历史
export const exportChatHistoryApi = (sessionId, format = 'txt') => {
  return axios.get(`/api/ai/sessions/${sessionId}/history/export/`, {
    params: { format },
    responseType: 'blob'
  })
}

export default {
  // 会话管理
  createAISessionApi,
  getAISessionListApi,
  getAISessionDetailApi,
  updateAISessionApi,
  deleteAISessionApi,

  // 测试用例管理
  getAIGeneratedCasesApi,
  getAIGeneratedCaseDetailApi,
  updateAIGeneratedCaseApi,
  deleteAIGeneratedCaseApi,

  // 核心功能
  generateTestCasesApi,
  acceptTestCasesApi,
  optimizeTestCaseApi,
  analyzeCoverageApi,

  // 对话功能
  chatWithAIApi,
  getChatHistoryApi,

  // 统计功能
  getAIUsageStatisticsApi,

  // 快捷操作
  quickGenerateTestCasesApi,
  batchAcceptTestCasesApi,
  smartOptimizeTestCaseApi,
  analyzeProjectCoverageApi,
  sendMessageToAIApi,

  // 配置管理
  getAIConfigApi,
  updateAIConfigApi,

  // 状态检查
  checkAIServiceStatusApi,
  getAIModelInfoApi,

  // 重试机制
  generateTestCasesWithRetryApi,
  chatWithAIWithRetryApi,

  // 批量操作
  batchUpdateAICasesStatusApi,
  batchDeleteAICasesApi,
  batchRateAICasesApi,

  // 导出功能
  exportAIGeneratedCasesApi,
  exportChatHistoryApi
}
