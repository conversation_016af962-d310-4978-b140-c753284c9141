#!/usr/bin/env python
# coding=utf-8
"""
测试RAGFlow响应处理
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/Users/<USER>/idp/kscloud/code/teamvision/teamvision_ezone/teamvision_1.0_dev/teamvision')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')
django.setup()

from teamvision.ai.ragflow_client_sdk import RAGFlowClient
from gatesidelib.common.simplelogger import SimpleLogger

def test_ragflow_response():
    """测试RAGFlow响应类型"""
    try:
        print("=== 测试RAGFlow响应处理 ===")
        
        # 创建agent客户端
        client = RAGFlowClient(mode="agent")
        
        # 测试问题
        question = "请帮我生成一个简单的登录功能测试用例"
        
        print(f"发送问题: {question}")
        
        # 测试非流式响应
        print("\n--- 测试非流式响应 (stream=False) ---")
        response = client.ask(question, stream=False)
        print(f"响应类型: {type(response)}")
        print(f"响应内容: {response}")
        
        # 测试流式响应
        print("\n--- 测试流式响应 (stream=True) ---")
        response_stream = client.ask(question, stream=True)
        print(f"流式响应类型: {type(response_stream)}")
        print(f"流式响应内容: {response_stream}")
        
    except Exception as e:
        SimpleLogger.exception(f"测试失败: {str(e)}")
        print(f"测试失败: {str(e)}")

if __name__ == "__main__":
    test_ragflow_response()
