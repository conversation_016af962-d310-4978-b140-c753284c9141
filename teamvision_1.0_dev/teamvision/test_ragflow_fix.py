#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试RAGFlow流式响应修复
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('/Users/<USER>/idp/kscloud/code/teamvision/teamvision_ezone/teamvision_1.0_dev/teamvision')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'teamvision.settings')
django.setup()

from teamvision.ai.ragflow_client_sdk import RAGFlowClient
from gatesidelib.common.simplelogger import SimpleLogger

def test_ragflow_client():
    """测试RAGFlow客户端的流式和非流式响应"""
    
    print("🚀 开始测试RAGFlow客户端...")
    
    try:
        # 测试Agent模式
        print("\n📋 测试Agent模式...")
        agent_client = RAGFlowClient(mode="agent")
        
        # 健康检查
        health = agent_client.health_check()
        print(f"健康检查结果: {health}")
        
        if health['status'] == 'healthy':
            # 测试非流式响应
            print("\n🔄 测试非流式响应...")
            response = agent_client.ask("请帮我生成测试用例", stream=False)
            print(f"非流式响应: {response[:100]}..." if len(response) > 100 else f"非流式响应: {response}")
            
            # 测试流式响应
            print("\n🌊 测试流式响应...")
            stream_response = agent_client.ask("请帮我生成测试用例", stream=True)
            print(f"流式响应: {stream_response[:100]}..." if len(stream_response) > 100 else f"流式响应: {stream_response}")
        else:
            print("❌ Agent客户端健康检查失败，跳过测试")
            
    except Exception as e:
        print(f"❌ Agent模式测试失败: {str(e)}")
        SimpleLogger.exception("Agent模式测试异常")
    
    try:
        # 测试Chat模式
        print("\n📋 测试Chat模式...")
        chat_client = RAGFlowClient(mode="chat")
        
        # 健康检查
        health = chat_client.health_check()
        print(f"健康检查结果: {health}")
        
        if health['status'] == 'healthy':
            # 测试非流式响应
            print("\n🔄 测试非流式响应...")
            response = chat_client.ask("请帮我生成测试用例", stream=False)
            print(f"非流式响应: {response[:100]}..." if len(response) > 100 else f"非流式响应: {response}")
            
            # 测试流式响应
            print("\n🌊 测试流式响应...")
            stream_response = chat_client.ask("请帮我生成测试用例", stream=True)
            print(f"流式响应: {stream_response[:100]}..." if len(stream_response) > 100 else f"流式响应: {stream_response}")
        else:
            print("❌ Chat客户端健康检查失败，跳过测试")
            
    except Exception as e:
        print(f"❌ Chat模式测试失败: {str(e)}")
        SimpleLogger.exception("Chat模式测试异常")
    
    print("\n✅ 测试完成！")

if __name__ == "__main__":
    test_ragflow_client()
