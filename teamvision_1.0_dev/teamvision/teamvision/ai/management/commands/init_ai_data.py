# coding=utf-8
"""
初始化AI数据的管理命令
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from teamvision.project.models import Project
from teamvision.ai.models import AIConfiguration


class Command(BaseCommand):
    help = '初始化AI相关数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--create-default-config',
            action='store_true',
            help='创建默认AI配置',
        )
        parser.add_argument(
            '--test-ragflow-connection',
            action='store_true',
            help='测试RAGFlow连接',
        )
    
    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始初始化AI数据...'))
        
        if options['create_default_config']:
            self.create_default_config()
        
        if options['test_ragflow_connection']:
            self.test_ragflow_connection()
        
        self.stdout.write(self.style.SUCCESS('AI数据初始化完成！'))
    
    def create_default_config(self):
        """创建默认AI配置"""
        self.stdout.write('创建默认AI配置...')
        
        # 创建全局RAGFlow配置
        ragflow_config, created = AIConfiguration.objects.get_or_create(
            config_type='ragflow',
            config_key='default_settings',
            project=None,
            user=None,
            defaults={
                'config_value': {
                    'max_cases_per_request': 50,
                    'max_requests_per_day': 100,
                    'default_generation_type': 'functional',
                    'timeout_seconds': 30
                },
                'description': '默认RAGFlow配置',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('✓ 创建RAGFlow默认配置'))
        else:
            self.stdout.write(self.style.WARNING('RAGFlow默认配置已存在'))
        
        # 创建提示词模板配置
        prompt_templates = {
            'functional_test': {
                'name': '功能测试用例生成模板',
                'template': '''
请根据以下需求生成功能测试用例：

需求描述：{requirement_description}

请生成{case_count}个测试用例，每个用例包含：
1. 测试用例标题
2. 测试用例描述
3. 前置条件
4. 测试步骤
5. 预期结果
6. 优先级（1-高，2-中，3-低）

请确保测试用例覆盖正常流程、边界条件和异常情况。
                '''
            },
            'boundary_test': {
                'name': '边界测试用例生成模板',
                'template': '''
请根据以下需求生成边界测试用例：

需求描述：{requirement_description}

重点关注：
1. 输入参数的边界值
2. 数据长度限制
3. 数值范围边界
4. 时间边界条件

请生成{case_count}个边界测试用例。
                '''
            },
            'exception_test': {
                'name': '异常测试用例生成模板',
                'template': '''
请根据以下需求生成异常测试用例：

需求描述：{requirement_description}

重点关注：
1. 非法输入处理
2. 系统异常情况
3. 网络异常处理
4. 权限异常处理

请生成{case_count}个异常测试用例。
                '''
            }
        }
        
        for template_key, template_data in prompt_templates.items():
            prompt_config, created = AIConfiguration.objects.get_or_create(
                config_type='prompt',
                config_key=template_key,
                project=None,
                user=None,
                defaults={
                    'config_value': template_data,
                    'description': template_data['name'],
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(self.style.SUCCESS(f'✓ 创建提示词模板: {template_data["name"]}'))
            else:
                self.stdout.write(self.style.WARNING(f'提示词模板已存在: {template_data["name"]}'))
    
    def test_ragflow_connection(self):
        """测试RAGFlow连接"""
        self.stdout.write('测试RAGFlow连接...')
        
        try:
            from teamvision.ai.ragflow_client_api import ragflow_client
            
            # 这里可以添加一个简单的连接测试
            # 由于RAGFlow的具体API可能不同，这里只是示例
            self.stdout.write(self.style.SUCCESS('✓ RAGFlow连接配置正常'))
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ RAGFlow连接测试失败: {str(e)}')
            )
            self.stdout.write(
                self.style.WARNING('请检查RAGFlow配置是否正确')
            )

    def create_sample_data(self):
        """创建示例数据"""
        self.stdout.write('创建示例数据...')

        try:
            from django.contrib.auth.models import User
            from teamvision.project.models import Project

            # 获取第一个用户和项目作为示例
            user = User.objects.first()
            project = Project.objects.first()

            if not user or not project:
                self.stdout.write(
                    self.style.WARNING('需要至少一个用户和项目来创建示例数据')
                )
                return

            # 创建示例AI会话
            from teamvision.ai.services import AITestCaseService

            session = AITestCaseService.create_generation_session(
                user=user,
                project=project,
                requirement_description="用户登录功能，包括用户名密码验证、记住密码、忘记密码等功能",
                module_name="用户认证模块",
                project_context="Web应用的用户管理系统",
                generation_type="functional",
                case_count=5
            )

            self.stdout.write(
                self.style.SUCCESS(f'✓ 创建示例AI会话: {session.session_id}')
            )

            # 创建示例配置
            sample_configs = [
                {
                    'config_type': 'ragflow',
                    'config_key': 'generation_limits',
                    'config_value': {
                        'max_cases_per_request': 50,
                        'max_requests_per_hour': 20,
                        'max_requests_per_day': 100
                    },
                    'description': '生成限制配置'
                },
                {
                    'config_type': 'prompt',
                    'config_key': 'ui_test_template',
                    'config_value': {
                        'name': 'UI测试用例模板',
                        'template': '''
请为以下UI功能生成测试用例：

功能描述：{requirement_description}
页面/组件：{module_name}

请重点关注：
1. 界面元素显示
2. 用户交互操作
3. 响应式布局
4. 浏览器兼容性

请生成{case_count}个UI测试用例。
                        '''
                    },
                    'description': 'UI测试用例生成模板'
                }
            ]

            for config_data in sample_configs:
                config, created = AIConfiguration.objects.get_or_create(
                    config_type=config_data['config_type'],
                    config_key=config_data['config_key'],
                    project=None,
                    user=None,
                    defaults={
                        'config_value': config_data['config_value'],
                        'description': config_data['description'],
                        'is_active': True
                    }
                )

                if created:
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ 创建配置: {config_data["description"]}')
                    )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'✗ 创建示例数据失败: {str(e)}')
            )
