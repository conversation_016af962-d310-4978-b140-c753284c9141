# coding=utf-8
"""
AI相关的数据模型
"""

from django.db import models
from django.contrib.auth.models import User
from teamvision.project.models import ProjectTestCase


class AIGenerationSession(models.Model):
    """AI生成会话"""
    
    SESSION_STATUS_CHOICES = [
        ('active', '进行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]
    
    GENERATION_TYPE_CHOICES = [
        ('functional', '功能测试'),
        ('boundary', '边界测试'),
        ('exception', '异常测试'),
        ('performance', '性能测试'),
        ('security', '安全测试'),
        ('supplement', '用例补充'),
        ('analysis', '用例分析'),
    ]
    
    id = models.AutoField(primary_key=True)
    session_id = models.CharField(max_length=100, unique=True, verbose_name='会话ID')
    status = models.CharField(max_length=20, choices=SESSION_STATUS_CHOICES, default='active',verbose_name='状态')

    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    project = models.IntegerField(blank=True, null=True, verbose_name='项目')
    
    # 生成参数
    requirement_description = models.TextField(verbose_name='需求描述')
    generation_type = models.CharField(max_length=20, choices=GENERATION_TYPE_CHOICES, default='functional',verbose_name='生成类型')
  
    # RAGFlow相关
    ragflow_session_id = models.CharField(max_length=200, blank=True, verbose_name='RAGFlow会话ID')
    ragflow_generation_id = models.CharField(max_length=200, blank=True, verbose_name='RAGFlow生成ID')
    
    # 生成结果统计
    generated_count = models.IntegerField(default=0, verbose_name='已生成数量')
    accepted_count = models.IntegerField(default=0, verbose_name='已采纳数量')
    
    # 时间戳
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    completed_time = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')
    
    # 元数据
    metadata = models.JSONField(default=dict, blank=True, verbose_name='元数据')
    
    class Meta:
        db_table = 'ai_generation_session'
        verbose_name = 'AI生成会话'
        verbose_name_plural = 'AI生成会话'
        ordering = ['-created_time']
    
    def __str__(self):
        return f"{self.session_id} - {self.project.PBTitle}"


class AIGeneratedTestCase(models.Model):
    """AI生成的测试用例"""
    
    STATUS_CHOICES = [
        ('generated', '已生成'),
        ('reviewed', '已评审'),
        ('accepted', '已采纳'),
        ('rejected', '已拒绝'),
        ('modified', '已修改'),
    ]
    
    id = models.AutoField(primary_key=True)
    session = models.ForeignKey(AIGenerationSession,  on_delete=models.CASCADE,  related_name='generated_cases', verbose_name='生成会话')
    
    # 测试用例内容
    title = models.CharField(max_length=500, verbose_name='标题')
    description = models.TextField(verbose_name='描述')
    precondition = models.TextField(blank=True, verbose_name='前置条件')
    test_steps = models.JSONField(default=list, verbose_name='测试步骤')
    expected_result = models.TextField(verbose_name='预期结果')
    priority = models.IntegerField(default=2, verbose_name='优先级')

    # 状态管理
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='generated',verbose_name='状态')
    
    # 关联的正式测试用例
    project_test_case = models.ForeignKey(ProjectTestCase, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联测试用例')
    
    # AI生成相关
    ai_confidence = models.FloatField(default=0.0, verbose_name='AI置信度')
    generation_prompt = models.TextField(blank=True, verbose_name='生成提示词')
    
    # 用户反馈
    user_rating = models.IntegerField(null=True, blank=True, verbose_name='用户评分')
    user_feedback = models.TextField(blank=True, verbose_name='用户反馈')
    
    # 时间戳
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    # 元数据
    metadata = models.JSONField(default=dict, blank=True, verbose_name='元数据')
    
    class Meta:
        db_table = 'ai_generated_test_case'
        verbose_name = 'AI生成测试用例'
        verbose_name_plural = 'AI生成测试用例'
        ordering = ['-created_time']
    
    def __str__(self):
        return f"{self.title} ({self.status})"


class AIChatHistory(models.Model):
    """AI对话历史"""
    
    MESSAGE_TYPE_CHOICES = [
        ('user', '用户消息'),
        ('assistant', 'AI助手消息'),
        ('system', '系统消息'),
    ]
    
    id = models.AutoField(primary_key=True)
    session = models.ForeignKey(to="AIGenerationSession", on_delete=models.CASCADE, related_name='chat_history', db_column="session", db_constraint=False,verbose_name='生成会话')

    # 消息内容
    message_type = models.CharField( max_length=20,  choices=MESSAGE_TYPE_CHOICES, verbose_name='消息类型')
    content = models.TextField(verbose_name='消息内容')
    
    # RAGFlow相关
    ragflow_message_id = models.CharField(max_length=200, blank=True, verbose_name='RAGFlow消息ID')
    
    # 时间戳
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    # 元数据
    metadata = models.JSONField(default=dict, blank=True, verbose_name='元数据')
    
    class Meta:
        db_table = 'ai_chat_history'
        verbose_name = 'AI对话历史'
        verbose_name_plural = 'AI对话历史'
        ordering = ['created_time']
    
    def __str__(self):
        return f"{self.message_type}: {self.content[:50]}..."


class AIConfiguration(models.Model):
    """AI配置"""
    
    CONFIG_TYPE_CHOICES = [
        ('ragflow', 'RAGFlow配置'),
        ('prompt', '提示词配置'),
        ('model', '模型配置'),
    ]
    
    id = models.AutoField(primary_key=True)
    config_type = models.CharField(max_length=20, choices=CONFIG_TYPE_CHOICES, verbose_name='配置类型')
    config_key = models.CharField(max_length=100, verbose_name='配置键')
    config_value = models.JSONField(verbose_name='配置值')
    description = models.TextField(blank=True, verbose_name='描述')
    
    # 作用域
    project = models.IntegerField(null=True, blank=True, verbose_name='项目')
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, verbose_name='用户')
    
    # 状态
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    
    # 时间戳
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'ai_configuration'
        verbose_name = 'AI配置'
        verbose_name_plural = 'AI配置'
        unique_together = ['config_type', 'config_key', 'project', 'user']
        ordering = ['-updated_time']
    
    def __str__(self):
        return f"{self.config_type}.{self.config_key}"


class AIUsageStatistics(models.Model):
    """AI使用统计"""
    
    id = models.AutoField(primary_key=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户')
    project = models.IntegerField(verbose_name='项目')
    
    # 统计数据
    date = models.DateField(verbose_name='日期')
    generation_count = models.IntegerField(default=0, verbose_name='生成次数')
    generated_cases_count = models.IntegerField(default=0, verbose_name='生成用例数')
    accepted_cases_count = models.IntegerField(default=0, verbose_name='采纳用例数')
    chat_messages_count = models.IntegerField(default=0, verbose_name='对话消息数')
    
    # API调用统计
    api_calls_count = models.IntegerField(default=0, verbose_name='API调用次数')
    api_success_count = models.IntegerField(default=0, verbose_name='API成功次数')
    api_error_count = models.IntegerField(default=0, verbose_name='API错误次数')
    
    # 时间统计（秒）
    total_generation_time = models.IntegerField(default=0, verbose_name='总生成时间')
    average_generation_time = models.FloatField(default=0.0, verbose_name='平均生成时间')
    
    # 时间戳
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        db_table = 'ai_usage_statistics'
        verbose_name = 'AI使用统计'
        verbose_name_plural = 'AI使用统计'
        unique_together = ['user', 'project', 'date']
        ordering = ['-date']
    
    def __str__(self):
        return f"{self.user.username} - {self.project.PBTitle} - {self.date}"
